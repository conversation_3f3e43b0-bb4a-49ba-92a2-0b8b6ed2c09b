import React, { Suspense } from 'react';
import { motion, useReducedMotion, useScroll, useTransform } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight, Brain, Lightbulb, Zap, Wrench, GraduationCap, Sparkles, Users } from 'lucide-react';
import { Button } from './ui/button';

const NetworkAnimation = React.lazy(() => import('./NetworkAnimation'));

const Hero = () => {
  const shouldReduceMotion = useReducedMotion();
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 500], [0, 100]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-b from-midnight to-darkShade">
      {/* Interactive Network Background */}
      <div className="absolute inset-0 opacity-40">
        <Suspense fallback={<div className="w-full h-full bg-midnight" />}>
          <NetworkAnimation />
        </Suspense>
      </div>
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-midnight/20 to-midnight/60" />
      
      <motion.div 
        className="relative z-10 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto text-center"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        style={{ y, opacity }}
      >
        {/* Impact Badge */}
        <motion.div 
          className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 rounded-full bg-gradient-to-r from-accent/20 to-accent/10 border border-accent/30 mb-6 sm:mb-8 backdrop-blur-sm"
          variants={itemVariants}
          whileHover={{ scale: 1.05 }}
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          >

          </motion.div>
          <span className="text-xs sm:text-sm font-medium text-accent">
            The Sainpse Institute of Augmented Intelligence
          </span>
        </motion.div>
        
        {/* Main Headline */}
        <motion.h1 
          className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold mb-4 sm:mb-6 leading-tight px-2"
          variants={itemVariants}
        >
          <span className="block text-snow">The Future is</span>
          <span className="block bg-gradient-to-r from-accent via-accent/80 to-accent bg-clip-text text-transparent">
            Collaborative Intelligence
          </span>
        </motion.h1>
        
        {/* Powerful Subheadline */}
        <motion.p 
          className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-snow/90 max-w-4xl mx-auto leading-relaxed font-medium px-2"
          variants={itemVariants}
        >
          Join a global network of visionaries who are 
          <span className="text-accent font-bold"> redefining what's possible</span> when human creativity and AI unite.
        </motion.p>

        {/* Urgency Statement */}
        <motion.div 
          className="mb-8 sm:mb-12 max-w-3xl mx-auto px-2"
          variants={itemVariants}
        >
          <p className="text-sm sm:text-base md:text-lg text-snow/70 leading-relaxed">
            The revolution has begun. Companies embracing augmented intelligence are 
            <span className="text-accent font-semibold"> 3x more innovative</span> and 
            <span className="text-accent font-semibold"> 5x faster to market</span>. 
            Don't get left behind.
          </p>
        </motion.div>

        {/* Enhanced CTAs with Urgency */}
        <motion.div 
          className="flex flex-col sm:flex-row gap-4 justify-center mb-12 sm:mb-16"
          variants={itemVariants}
        >
          <Link to="/tools" className="w-full sm:w-auto">
            <Button
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 text-white px-8 sm:px-10 py-4 sm:py-5 text-base sm:text-lg font-bold rounded-full shadow-2xl hover:shadow-accent/25 transition-all duration-300 group relative overflow-hidden"
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.6 }}
              />
              <Wrench className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:rotate-12" />
              Start Building Now
              <ArrowRight className="ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
          <Link to="/education" className="w-full sm:w-auto">
            <Button
              size="lg"
              variant="outline"
              className="w-full sm:w-auto border-2 border-accent/50 text-accent hover:bg-accent hover:text-white px-8 sm:px-10 py-4 sm:py-5 text-base sm:text-lg font-bold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 group backdrop-blur-sm"
            >
              <GraduationCap className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:-rotate-12" />
              Master AI Today
              <ArrowRight className="ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </motion.div>

        {/* Compelling Scroll Indicator */}
        <motion.div
          className="flex flex-col items-center gap-2 mt-8 sm:mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5 }}
          variants={itemVariants}
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center gap-2"
          >
            <span className="text-xs text-accent font-semibold uppercase tracking-wide">
              Discover Your Potential
            </span>
            <div className="w-6 h-10 rounded-full border-2 border-accent/40 flex justify-center p-1 bg-accent/5">
              <motion.div
                className="w-1 h-2 rounded-full bg-accent"
                animate={{ y: [0, 16, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              />
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Hero;
