import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Bot, Code, Database, Network, ArrowRight, Sparkles, Zap, Brain, Cpu, Star, Layers } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from './ui/button';

const services = [
  {
    icon: Bot,
    title: 'Modernize',
    description: 'Building intelligent websites and integrating AI-driven customer support agents to enhance user experience and engagement.',
    link: '/services/modernize',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['AI Chatbots', 'Smart Websites', '24/7 Support'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Brain
  },
  {
    icon: Network,
    title: 'Automation',
    description: 'Automating repetitive tasks and streamlining workflows to enhance efficiency and scalability.',
    link: '/services/automation',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['Workflow Automation', 'Process Optimization', 'Smart Integration'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Cpu
  },
  {
    icon: Code,
    title: 'AI Development',
    description: 'Advancing Large Language Models (LLMs) and integrating cutting-edge AI into real-world applications for transformative impact.',
    link: '/services/ai-development',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['Custom LLMs', 'AI Integration', 'Model Training'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Star
  },
  {
    icon: Database,
    title: 'Data Science',
    description: 'Transforming raw data into actionable intelligence through advanced analytics and machine learning.',
    link: '/services/data-science',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['Data Analytics', 'ML Solutions', 'Business Intelligence'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Layers
  },
];

const Services = () => {
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const headerY = useTransform(scrollYProgress, [0, 0.3], [0, -30]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const fadeInUp = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180, opacity: 0 },
    visible: {
      scale: 1,
      rotate: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20,
        delay: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { y: 40, opacity: 0, scale: 0.95 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section 
      ref={containerRef}
      id="services" 
      className="py-20 md:py-32 bg-gradient-to-b from-midnight via-darkShade to-midnight relative overflow-hidden"
    >
      {/* Enhanced animated background decoration */}
      <motion.div 
        className="absolute inset-0"
        style={{ y: backgroundY }}
      >
        <motion.div 
          className="absolute inset-0 opacity-15"
          animate={{
            background: [
              "radial-gradient(circle at 30% 30%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 70% 30%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 30% 70%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 50%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 60%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 30% 30%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
            ]
          }}
          transition={{ duration: 15, repeat: Infinity }}
        />
        
        {/* Minimal floating elements */}
        <motion.div className="absolute inset-0">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-accent/15 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, 20, -20],
                opacity: [0.1, 0.3, 0.1],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 6 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 3,
              }}
            />
          ))}
        </motion.div>
      </motion.div>

      <div className="max-w-7xl mx-auto relative z-10 px-6 lg:px-8">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16 md:mb-24"
          style={{ y: headerY }}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div 
            className="inline-flex items-center gap-3 px-4 py-2 md:px-6 md:py-3 rounded-full bg-white/5 border border-accent/20 mb-6 md:mb-8 backdrop-blur-sm"
            variants={fadeInUp}
            whileHover={{ scale: 1.02, y: -1 }}
          >
            <Sparkles className="w-4 h-4 md:w-5 md:h-5 text-accent/80" />
            <span className="text-sm md:text-base font-medium text-accent/80">What We Offer</span>
          </motion.div>

          <motion.h2 
            className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 text-snow px-4"
            variants={fadeInUp}
          >
            Our{' '}
            <motion.span 
              className="text-accent"
              animate={{ 
                opacity: [1, 0.8, 1]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Services
            </motion.span>
          </motion.h2>

          <motion.p 
            className="text-lg md:text-xl lg:text-2xl text-snow/60 max-w-4xl mx-auto px-4 leading-relaxed"
            variants={fadeInUp}
          >
            Empowering businesses with{' '}
            <span className="text-snow/80 font-medium">cutting-edge AI solutions</span>
            {' '}and intelligent automation.
          </motion.p>
        </motion.div>

        {/* Enhanced Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            const AccentIcon = service.accentIcon;
            return (
              <motion.div
                key={service.title}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-100px" }}
                variants={cardVariants}
                transition={{ delay: index * 0.1 }}
                className="group relative"
              >
                <motion.div
                  className={`relative p-6 md:p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-accent/30 transition-all duration-500 overflow-hidden cursor-pointer h-full group`}
                  whileHover={{ 
                    y: -4, 
                    scale: 1.01,
                  }}
                  whileTap={{ scale: 0.99 }}
                >
                  {/* Minimal background gradient on hover */}
                  <motion.div 
                    className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                  />
                  
                  {/* Service content */}
                  <div className="relative z-10 h-full flex flex-col">
                    {/* Minimal icon container */}
                    <motion.div
                      className={`relative w-14 h-14 md:w-16 md:h-16 rounded-xl bg-gradient-to-br ${service.gradient} flex items-center justify-center mb-6 shadow-sm group-hover:shadow-lg transition-all duration-500`}
                      variants={iconVariants}
                      whileHover={{ 
                        scale: 1.05,
                        rotate: 2
                      }}
                    >
                      <motion.div
                        whileHover={{ rotate: 90 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Icon className="w-7 h-7 md:w-8 md:h-8 text-white" />
                      </motion.div>
                      
                      {/* Subtle accent icon */}
                      <motion.div
                        className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <AccentIcon className="w-2.5 h-2.5 text-white/80" />
                      </motion.div>
                    </motion.div>

                    {/* Title and description */}
                    <div className="flex-grow">
                      <motion.h3 
                        className="text-xl md:text-2xl font-bold mb-3 text-snow group-hover:text-accent transition-colors duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 + index * 0.1 }}
                      >
                        {service.title}
                      </motion.h3>
                      
                      <motion.p 
                        className="text-sm md:text-base text-snow/80 group-hover:text-snow/90 transition-colors duration-300 mb-6 leading-relaxed"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        {service.description}
                      </motion.p>

                      {/* Minimal features list */}
                      <motion.div 
                        className="space-y-2 mb-6"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: 0.4 + index * 0.1 }}
                      >
                        {service.features.map((feature, idx) => (
                          <motion.div 
                            key={idx} 
                            className="flex items-center gap-2"
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.5 + index * 0.1 + idx * 0.05 }}
                          >
                            <div className="w-1 h-1 rounded-full bg-accent/60" />
                            <span className="text-xs md:text-sm text-snow/60 group-hover:text-snow/70 transition-colors duration-300">
                              {feature}
                            </span>
                          </motion.div>
                        ))}
                      </motion.div>
                    </div>

                    {/* Minimal CTA link */}
                    <Link
                      to={service.link}
                      className="inline-flex items-center text-accent/80 hover:text-accent transition-all duration-300 group/link mt-auto"
                    >
                      <motion.span 
                        className="relative text-sm md:text-base font-medium"
                        whileHover={{ x: 2 }}
                      >
                        Learn more
                        <motion.span
                          className="absolute bottom-0 left-0 w-0 h-px bg-accent/40 group-hover/link:w-full transition-all duration-300"
                          initial={{ width: '0%' }}
                          whileHover={{ width: '100%' }}
                        />
                      </motion.span>
                      <ArrowRight className="w-3.5 h-3.5 ml-1.5 transition-transform group-hover/link:translate-x-1" />
                    </Link>
                  </div>

                  {/* Minimal decorative elements */}
                  <div className="absolute -top-4 -right-4 w-12 h-12 rounded-full bg-accent/5 group-hover:bg-accent/10 transition-colors duration-500" />
                </motion.div>
              </motion.div>
            );
          })}
        </div>

        {/* Minimal CTA Section */}
        <motion.div
          className="text-center mt-16 md:mt-24"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div
            className="relative inline-block"
            variants={fadeInUp}
            whileHover={{ 
              scale: 1.02,
              y: -1
            }}
            whileTap={{ scale: 0.99 }}
          >
            <Button
              size="lg"
              className="relative bg-accent text-black hover:bg-accent/90 transition-all duration-300 px-8 py-4 text-base font-medium shadow-sm hover:shadow-md"
            >
              <span className="relative z-10">
                Explore All Services
              </span>
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </motion.div>
          
          <motion.p
            className="text-sm md:text-base text-snow/50 mt-4 max-w-lg mx-auto"
            variants={fadeInUp}
          >
            Ready to transform your business with AI?
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;