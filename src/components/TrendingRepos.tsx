import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { 
  Github, 
  Star, 
  GitFork, 
  Calendar, 
  TrendingUp, 
  Code2, 
  ExternalLink, 
  Sparkles,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { Button } from './ui/button';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  html_url: string;
  stargazers_count: number;
  forks_count: number;
  language: string;
  created_at: string;
  updated_at: string;
  topics: string[];
  owner: {
    login: string;
    avatar_url: string;
  };
}

// Fallback data for when GitHub API is unavailable
const getFallbackRepos = (): Repository[] => [
  {
    id: 1,
    name: 'tensorflow',
    full_name: 'tensorflow/tensorflow',
    description: 'An Open Source Machine Learning Framework for Everyone',
    html_url: 'https://github.com/tensorflow/tensorflow',
    stargazers_count: 185000,
    forks_count: 74000,
    language: 'C++',
    created_at: '2015-11-07T01:05:27Z',
    updated_at: '2024-01-15T10:30:00Z',
    topics: ['machine-learning', 'deep-learning', 'tensorflow', 'python'],
    owner: {
      login: 'tensorflow',
      avatar_url: 'https://avatars.githubusercontent.com/u/15658638?v=4'
    }
  },
  {
    id: 2,
    name: 'pytorch',
    full_name: 'pytorch/pytorch',
    description: 'Tensors and Dynamic neural networks in Python with strong GPU acceleration',
    html_url: 'https://github.com/pytorch/pytorch',
    stargazers_count: 82000,
    forks_count: 22000,
    language: 'Python',
    created_at: '2016-08-13T01:05:27Z',
    updated_at: '2024-01-15T08:20:00Z',
    topics: ['deep-learning', 'machine-learning', 'pytorch', 'python'],
    owner: {
      login: 'pytorch',
      avatar_url: 'https://avatars.githubusercontent.com/u/21003710?v=4'
    }
  },
  {
    id: 3,
    name: 'transformers',
    full_name: 'huggingface/transformers',
    description: 'State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX',
    html_url: 'https://github.com/huggingface/transformers',
    stargazers_count: 133000,
    forks_count: 26000,
    language: 'Python',
    created_at: '2018-10-29T01:05:27Z',
    updated_at: '2024-01-15T12:45:00Z',
    topics: ['transformers', 'nlp', 'pytorch', 'tensorflow', 'machine-learning'],
    owner: {
      login: 'huggingface',
      avatar_url: 'https://avatars.githubusercontent.com/u/25720743?v=4'
    }
  },
  {
    id: 4,
    name: 'scikit-learn',
    full_name: 'scikit-learn/scikit-learn',
    description: 'scikit-learn: machine learning in Python',
    html_url: 'https://github.com/scikit-learn/scikit-learn',
    stargazers_count: 59000,
    forks_count: 25000,
    language: 'Python',
    created_at: '2010-08-17T01:05:27Z',
    updated_at: '2024-01-15T14:20:00Z',
    topics: ['machine-learning', 'python', 'scikit-learn', 'data-science'],
    owner: {
      login: 'scikit-learn',
      avatar_url: 'https://avatars.githubusercontent.com/u/365630?v=4'
    }
  },
  {
    id: 5,
    name: 'keras',
    full_name: 'keras-team/keras',
    description: 'Deep Learning for humans',
    html_url: 'https://github.com/keras-team/keras',
    stargazers_count: 61000,
    forks_count: 19000,
    language: 'Python',
    created_at: '2015-03-27T01:05:27Z',
    updated_at: '2024-01-15T11:10:00Z',
    topics: ['deep-learning', 'keras', 'machine-learning', 'neural-networks'],
    owner: {
      login: 'keras-team',
      avatar_url: 'https://avatars.githubusercontent.com/u/34455048?v=4'
    }
  },
  {
    id: 6,
    name: 'stable-diffusion',
    full_name: 'CompVis/stable-diffusion',
    description: 'A latent text-to-image diffusion model',
    html_url: 'https://github.com/CompVis/stable-diffusion',
    stargazers_count: 67000,
    forks_count: 10000,
    language: 'Python',
    created_at: '2022-08-10T01:05:27Z',
    updated_at: '2024-01-15T16:30:00Z',
    topics: ['stable-diffusion', 'generative-ai', 'diffusion-models', 'text-to-image'],
    owner: {
      login: 'CompVis',
      avatar_url: 'https://avatars.githubusercontent.com/u/54370586?v=4'
    }
  }
];

const TrendingRepos = () => {
  const [repos, setRepos] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [usingFallback, setUsingFallback] = useState(false);

  const containerRef = React.useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const headerY = useTransform(scrollYProgress, [0, 0.3], [0, -30]);

  const fetchTrendingRepos = async () => {
    try {
      setError(null);
      
      // Try multiple search strategies for better success rate
      const searchQueries = [
        'topic:artificial-intelligence',
        'topic:machine-learning',
        'topic:deep-learning',
        'language:Python machine learning',
        'language:Python AI',
        'tensorflow OR pytorch OR keras'
      ];
      
      const sort = 'stars';
      const order = 'desc';
      const per_page = 6;
      
      let response;
      let lastError = null;
      
      // Try each query until one succeeds
      for (const query of searchQueries) {
        try {
          response = await fetch(
            `https://api.github.com/search/repositories?q=${encodeURIComponent(query)}&sort=${sort}&order=${order}&per_page=${per_page}`,
            {
              headers: {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'Sainpse-Landing-Page',
                // Add your GitHub token here for higher rate limits
                ...(import.meta.env.VITE_GITHUB_TOKEN && {
                  'Authorization': `token ${import.meta.env.VITE_GITHUB_TOKEN}`
                })
              }
            }
          );

          if (response.ok) {
            break; // Success, exit the loop
          } else {
            lastError = `GitHub API Error: ${response.status} ${response.statusText}`;
            console.warn(`Query failed: ${query} - ${lastError}`);
          }
        } catch (queryError) {
          lastError = queryError instanceof Error ? queryError.message : 'Network error';
          console.warn(`Query failed: ${query} - ${lastError}`);
        }
      }

      if (!response || !response.ok) {
        // If all queries failed, use fallback data
        console.warn('All GitHub API queries failed, using fallback data');
        setRepos(getFallbackRepos());
        setUsingFallback(true);
        return;
      }

      const data = await response.json();
      setRepos(data.items || []);
      setUsingFallback(false);
    } catch (err) {
      console.error('Error fetching trending repos:', err);
      // Use fallback data instead of showing error
      setRepos(getFallbackRepos());
      setUsingFallback(true);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTrendingRepos();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchTrendingRepos();
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      'Python': '#3776ab',
      'JavaScript': '#f1e05a',
      'TypeScript': '#2b7489',
      'Java': '#b07219',
      'C++': '#f34b7d',
      'Go': '#00ADD8',
      'Rust': '#dea584',
      'Swift': '#fa7343',
      'Kotlin': '#F18E33',
      'R': '#198ce7',
      'Jupyter Notebook': '#DA5B0B',
      'C': '#555555',
      'C#': '#239120',
      'PHP': '#4F5D95',
      'Ruby': '#701516',
    };
    return colors[language] || '#8b949e';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const fadeInUp = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const cardVariants = {
    hidden: { y: 40, opacity: 0, scale: 0.95 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section 
      ref={containerRef}
      id="trending-repos" 
      className="py-20 md:py-32 bg-gradient-to-b from-midnight via-darkShade to-midnight relative overflow-hidden"
    >
      {/* Enhanced animated background decoration */}
      <motion.div 
        className="absolute inset-0"
        style={{ y: backgroundY }}
      >
        <motion.div 
          className="absolute inset-0 opacity-15"
          animate={{
            background: [
              "radial-gradient(circle at 25% 25%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 75% 25%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 25% 75%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 50%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 30% 70%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 25% 25%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
            ]
          }}
          transition={{ duration: 20, repeat: Infinity }}
        />
        
        {/* Minimal floating elements */}
        <motion.div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-accent/15 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-15, 15, -15],
                opacity: [0.1, 0.3, 0.1],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 8 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 3,
              }}
            />
          ))}
        </motion.div>
      </motion.div>

      <div className="max-w-7xl mx-auto relative z-10 px-6 lg:px-8">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16 md:mb-24"
          style={{ y: headerY }}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div 
            className="inline-flex items-center gap-3 px-4 py-2 md:px-6 md:py-3 rounded-full bg-white/5 border border-accent/20 mb-6 md:mb-8 backdrop-blur-sm"
            variants={fadeInUp}
            whileHover={{ scale: 1.02, y: -1 }}
          >
            <TrendingUp className="w-4 h-4 md:w-5 md:h-5 text-accent/80" />
            <span className="text-sm md:text-base font-medium text-accent/80">Latest AI Innovations</span>
          </motion.div>

          <motion.h2 
            className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 text-snow px-4"
            variants={fadeInUp}
          >
            Trending{' '}
            <motion.span 
              className="text-accent"
              animate={{ 
                opacity: [1, 0.8, 1]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              AI Repositories
            </motion.span>
          </motion.h2>

          <motion.p 
            className="text-lg md:text-xl lg:text-2xl text-snow/60 max-w-4xl mx-auto px-4 leading-relaxed mb-8"
            variants={fadeInUp}
          >
            Discover the most innovative AI projects shaping the future of{' '}
            <span className="text-snow/80 font-medium">artificial intelligence</span>
            {usingFallback && (
              <span className="block text-sm text-yellow-400/70 mt-2">
                Showing popular repositories • Refresh for latest data
              </span>
            )}
          </motion.p>

          <motion.div
            className="flex justify-center"
            variants={fadeInUp}
          >
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              className="border-accent/30 text-accent hover:bg-accent hover:text-white transition-all duration-300"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          </motion.div>
        </motion.div>

        {/* Content */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <motion.div
              className="flex items-center space-x-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <RefreshCw className="w-6 h-6 text-accent animate-spin" />
              <span className="text-snow/60">Loading trending repositories...</span>
            </motion.div>
          </div>
        ) : repos.length === 0 ? (
          <motion.div
            className="flex flex-col items-center justify-center py-20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <AlertCircle className="w-12 h-12 text-yellow-400 mb-4" />
            <h3 className="text-xl font-semibold text-snow mb-2">Unable to Load Latest Data</h3>
            <p className="text-snow/60 text-center max-w-md mb-6">
              Showing popular AI repositories. Try refreshing for the latest data.
            </p>
            <Button
              onClick={handleRefresh}
              className="bg-accent hover:bg-accent/90 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </motion.div>
        ) : (
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={containerVariants}
          >
            {repos.map((repo, index) => (
              <motion.div
                key={repo.id}
                className="group relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:border-accent/30 transition-all duration-500 overflow-hidden"
                variants={cardVariants}
                whileHover={{ 
                  y: -8, 
                  scale: 1.02,
                  transition: { duration: 0.3 }
                }}
              >
                {/* Background gradient on hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                
                {/* Content */}
                <div className="relative z-10">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <img 
                        src={repo.owner.avatar_url} 
                        alt={repo.owner.login}
                        className="w-8 h-8 rounded-full"
                      />
                      <div>
                        <h3 className="text-lg font-semibold text-snow group-hover:text-accent transition-colors duration-300">
                          {repo.name}
                        </h3>
                        <p className="text-sm text-snow/60">{repo.owner.login}</p>
                      </div>
                    </div>
                    <Github className="w-5 h-5 text-snow/60 group-hover:text-accent transition-colors duration-300" />
                  </div>

                  {/* Description */}
                  <p className="text-snow/70 text-sm mb-4 line-clamp-3 leading-relaxed">
                    {repo.description || 'No description available'}
                  </p>

                  {/* Topics */}
                  {repo.topics && repo.topics.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-4">
                      {repo.topics.slice(0, 3).map((topic, topicIndex) => (
                        <span
                          key={topicIndex}
                          className="px-2 py-1 text-xs bg-accent/10 text-accent rounded-full border border-accent/20"
                        >
                          {topic}
                        </span>
                      ))}
                      {repo.topics.length > 3 && (
                        <span className="px-2 py-1 text-xs bg-white/5 text-snow/60 rounded-full border border-white/10">
                          +{repo.topics.length - 3}
                        </span>
                      )}
                    </div>
                  )}

                  {/* Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm text-snow/70">{formatNumber(repo.stargazers_count)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <GitFork className="w-4 h-4 text-snow/60" />
                        <span className="text-sm text-snow/70">{formatNumber(repo.forks_count)}</span>
                      </div>
                    </div>
                    {repo.language && (
                      <div className="flex items-center space-x-1">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: getLanguageColor(repo.language) }}
                        />
                        <span className="text-sm text-snow/70">{repo.language}</span>
                      </div>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-snow/50">
                      <Calendar className="w-3 h-3" />
                      <span>Updated {formatDate(repo.updated_at)}</span>
                    </div>
                    <a
                      href={repo.html_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center space-x-1 text-sm text-accent hover:text-accent/80 transition-colors duration-300"
                    >
                      <span>View</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* CTA Section */}
        <motion.div
          className="text-center mt-16 md:mt-24"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div
            className="relative inline-block"
            variants={fadeInUp}
            whileHover={{ 
              scale: 1.02,
              y: -1
            }}
            whileTap={{ scale: 0.99 }}
          >
            <a
              href="https://github.com/Sainpse"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Button
                size="lg"
                className="relative bg-accent text-black hover:bg-accent/90 transition-all duration-300 px-8 py-4 text-base font-medium shadow-sm hover:shadow-md"
              >
                <Github className="mr-2 h-4 w-4" />
                Explore Our GitHub
                <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </a>
          </motion.div>
          
          <motion.p
            className="text-sm md:text-base text-snow/50 mt-4 max-w-lg mx-auto"
            variants={fadeInUp}
          >
            Check out our open-source contributions and AI projects
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
};

export default TrendingRepos;
