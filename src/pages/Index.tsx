import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { motion, AnimatePresence } from 'framer-motion';
import { Aperture } from 'lucide-react';
import Hero from '../components/Hero';
import Mission from '../components/Mission';
import ValuesCarousel from '../components/ValuesCarousel';
import Services from '../components/Services';
import TrendingRepos from '../components/TrendingRepos';
import Footer from '../components/Footer';
import { Auth } from '../components/Auth';
import { Dialog, DialogContent } from '../components/ui/dialog';
import { useNavigate, Link } from 'react-router-dom';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

const Index = () => {
  const [showAuth, setShowAuth] = useState(false);
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUser(user);
          const { data: profile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          setProfile(profile);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setAuthLoading(false);
      }
    };

    fetchUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        setUser(session.user);
      } else {
        setUser(null);
        setProfile(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { href: '#mission', label: 'About Us' },
    { href: '#services', label: 'Services' },
    { href: '#trending-repos', label: 'AI Trends' },
    { href: '/education', label: 'Education' },
    { href: '#footer', label: 'Contact Us' },
  ];

  const headerVariants = {
    hidden: {
      y: -100,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1],
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: -20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const logoVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  const letterAnimation = {
    rest: {
      y: 0,
      transition: {
        duration: 0.1,
        type: "tween",
        ease: "easeIn"
      }
    },
    hover: {
      y: -3,
      transition: {
        duration: 0.1,
        type: "tween",
        ease: "easeOut"
      }
    }
  };

  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    setMenuOpen(false); // Close mobile menu

    // Check if the href is a hash link (internal anchor)
    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      if (element) {
        setTimeout(() => {
          const offsetTop = element.getBoundingClientRect().top + window.pageYOffset - 100;
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        }, 150);
      }
    } else {
      // Navigate to the route
      navigate(href);
    }
  };

  return (
    <div className="min-h-screen bg-midnight text-snow">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={headerVariants}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled ? 'bg-white/10 backdrop-blur-lg shadow-lg' : 'bg-white/5 backdrop-blur-md'
        }`}
      >
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <motion.div 
            variants={logoVariants}
            className="flex items-center"
          >
            <img src="/latest_logo2_black_NObg.png" alt="Sainpse Logo" className="w-10 h-10 mr-2" />
            <motion.h1 
              className="text-2xl font-bold tracking-tight"
              whileHover="hover"
              initial="rest"
            >
              {/* Animate each letter individually */}
              {"Sainpse".split("").map((letter, index) => (
                <motion.span
                  key={index}
                  variants={letterAnimation}
                  style={{ display: "inline-block" }}
                  custom={index}
                >
                  {letter}
                </motion.span>
              ))}
            </motion.h1>
          </motion.div>

          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link, index) => (
              link.href.startsWith('#') ? (
                <motion.a
                  key={link.href}
                  variants={itemVariants}
                  href={link.href}
                  onClick={(e) => handleNavClick(e, link.href)}
                  className="group relative text-sm font-medium transition-colors duration-200 hover:text-accent"
                  custom={index}
                  whileHover={{
                    scale: 1.05,
                    transition: { duration: 0.2 }
                  }}
                >
                  <motion.span
                    initial={{ y: 0 }}
                    whileHover={{ y: -2 }}
                    transition={{ duration: 0.2 }}
                    className="inline-block"
                  >
                    {link.label}
                  </motion.span>
                  <span className="absolute left-0 -bottom-1 w-full h-0.5 bg-gradient-to-r from-transparent via-emerald-500 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
                </motion.a>
              ) : (
                <motion.div
                  key={link.href}
                  variants={itemVariants}
                  custom={index}
                >
                  <Link
                    to={link.href}
                    className="group relative text-sm font-medium transition-colors duration-200 hover:text-accent"
                  >
                    <motion.span
                      initial={{ y: 0 }}
                      whileHover={{ y: -2 }}
                      transition={{ duration: 0.2 }}
                      className="inline-block"
                    >
                      {link.label}
                    </motion.span>
                    <span className="absolute left-0 -bottom-1 w-full h-0.5 bg-gradient-to-r from-transparent via-accent to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
                  </Link>
                </motion.div>
              )
            ))}
          </nav>

          <motion.button
            variants={itemVariants}
            className="md:hidden text-snow focus:outline-none"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {menuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </motion.button>
        </div>

        <AnimatePresence>
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="md:hidden bg-white/10 backdrop-blur-md border-t border-white/20"
            >
              <nav className="flex flex-col space-y-4 p-4">
                {navLinks.map((link, index) => (
                  link.href.startsWith('#') ? (
                    <motion.a
                      key={link.href}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1 }}
                      href={link.href}
                      onClick={(e) => handleNavClick(e, link.href)}
                      className="group relative text-base font-medium transition-colors duration-200 hover:text-accent px-4 py-2 w-full"
                    >
                      {link.label}
                      <span className="block h-0.5 bg-gradient-to-r from-transparent via-accent to-transparent mt-1 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
                    </motion.a>
                  ) : (
                    <Link
                      key={link.href}
                      to={link.href}
                      className="group relative text-base font-medium transition-colors duration-200 hover:text-accent px-4 py-2 w-full"
                    >
                      {link.label}
                      <span className="block h-0.5 bg-gradient-to-r from-transparent via-accent to-transparent mt-1 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
                    </Link>
                  )
                ))}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
      {/* Main Content */}
      <div className="pt-20">
        <Hero />
         <div id="services">
          <Services />
        </div>
        <div id="trending-repos">
          <TrendingRepos />
        </div>
        <ValuesCarousel />
        <div id="mission">
          <Mission />
        </div>

        <div id="footer">
          <Footer />
        </div>
      </div>
      <Dialog open={showAuth} onOpenChange={setShowAuth}>
        <DialogContent className="sm:max-w-[425px]">
          <Auth />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Index;
